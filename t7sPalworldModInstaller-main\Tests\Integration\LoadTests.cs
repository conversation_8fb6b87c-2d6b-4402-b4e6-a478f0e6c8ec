using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Integration
{
    /// <summary>
    /// Load testing for large mod collections and high-stress scenarios
    /// Tests system behavior under extreme load conditions
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Integration)]
    [Category(TestCategories.Performance)]
    public class LoadTests : TestBase
    {
        private UE4SSDetector? _detector;
        private EnhancedInstallationEngine? _installationEngine;
        private ModManagerService? _modManager;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            
            _detector = new UE4SSDetector(TestPalworldRoot, TestCacheManager!);
            _installationEngine = new EnhancedInstallationEngine(TestPalworldRoot, _detector, TestCacheManager!, TestLogger!);
            _modManager = new ModManagerService(_detector, _installationEngine, TestLogger!);
        }

        [TearDown]
        public override async Task TearDown()
        {
            _modManager?.Dispose();
            _installationEngine?.Dispose();
            _detector?.Dispose();
            await base.TearDown();
        }

        [Test]
        [Category("LongRunning")]
        public async Task MassiveModCollection_LoadTest()
        {
            // Arrange - Create 500 test mods
            const int modCount = 500;
            TestContext.WriteLine($"Creating {modCount} test mods...");
            
            var stopwatch = Stopwatch.StartNew();
            await CreateMassiveModCollectionAsync(modCount);
            stopwatch.Stop();
            
            TestContext.WriteLine($"Mod creation completed in {stopwatch.ElapsedMilliseconds}ms");

            // Act - Load and process all mods
            stopwatch.Restart();
            var detectionResult = await _detector!.DetectUE4SSAsync();
            stopwatch.Stop();

            // Assert
            detectionResult.UserMods.Should().HaveCount(modCount);
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(30)); // Should complete within 30 seconds
            
            TestContext.WriteLine($"Detection of {modCount} mods completed in {stopwatch.ElapsedMilliseconds}ms");
            TestContext.WriteLine($"Average detection time per mod: {stopwatch.ElapsedMilliseconds / (double)modCount:F2}ms");
        }

        [Test]
        [Category("LongRunning")]
        public async Task ConcurrentInstallationStress_LoadTest()
        {
            // Arrange - Create multiple mod archives for concurrent installation
            const int concurrentCount = 25;
            var modArchives = new List<string>();
            
            for (int i = 0; i < concurrentCount; i++)
            {
                var archive = await CreateTestModArchiveAsync($"StressMod{i:D3}", ModStructureType.UE4SS);
                modArchives.Add(archive);
            }

            // Act - Install all mods concurrently
            var stopwatch = Stopwatch.StartNew();
            var semaphore = new SemaphoreSlim(5); // Limit to 5 concurrent installations
            
            var installTasks = modArchives.Select(async (archive, index) =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var progress = new Progress<InstallationProgress>();
                    var options = new InstallationOptions();
                    return await _installationEngine!.InstallModAsync(archive, options, progress, CancellationToken.None);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var results = await Task.WhenAll(installTasks);
            stopwatch.Stop();

            // Assert
            results.Should().HaveCount(concurrentCount);
            results.Should().AllSatisfy(r => r.Should().NotBeNull());
            results.Should().AllSatisfy(r => r.CanRollback.Should().BeTrue());
            
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(5)); // Should complete within 5 minutes
            
            TestContext.WriteLine($"Concurrent installation of {concurrentCount} mods completed in {stopwatch.ElapsedMilliseconds}ms");
            TestContext.WriteLine($"Average time per mod: {stopwatch.ElapsedMilliseconds / (double)concurrentCount:F2}ms");
        }

        [Test]
        [Category("LongRunning")]
        public async Task MemoryPressure_LoadTest()
        {
            // Arrange
            const int iterations = 200;
            var initialMemory = GC.GetTotalMemory(true);
            var memoryReadings = new List<long>();

            // Act - Perform many memory-intensive operations
            for (int i = 0; i < iterations; i++)
            {
                // Create and install a mod
                var archive = await CreateTestModArchiveAsync($"MemPressureMod{i:D3}", ModStructureType.UE4SS);
                var options = new InstallationOptions();
                var result = await _installationEngine!.InstallModAsync(archive, options, null, CancellationToken.None);

                // Immediately rollback to prevent disk space issues
                await _installationEngine!.RollbackInstallationAsync(result.Id, CancellationToken.None);
                
                // Record memory usage every 20 iterations
                if (i % 20 == 0)
                {
                    var currentMemory = GC.GetTotalMemory(false);
                    memoryReadings.Add(currentMemory);
                    TestContext.WriteLine($"Iteration {i}: Memory usage: {currentMemory / 1024 / 1024:F2} MB");
                }
                
                // Force garbage collection every 50 iterations
                if (i % 50 == 0)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
            }

            // Final memory check
            GC.Collect();
            GC.WaitForPendingFinalizers();
            var finalMemory = GC.GetTotalMemory(true);

            // Assert
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreasePercent = (memoryIncrease * 100.0) / initialMemory;
            
            // Memory increase should be reasonable (less than 100% increase under stress)
            memoryIncreasePercent.Should().BeLessThan(100);
            
            // Memory should not continuously grow (check for memory leaks)
            var memoryGrowthTrend = CalculateMemoryGrowthTrend(memoryReadings);
            memoryGrowthTrend.Should().BeLessThan(1024 * 1024); // Less than 1MB growth per measurement
            
            TestContext.WriteLine($"Initial memory: {initialMemory / 1024 / 1024:F2} MB");
            TestContext.WriteLine($"Final memory: {finalMemory / 1024 / 1024:F2} MB");
            TestContext.WriteLine($"Memory increase: {memoryIncrease / 1024 / 1024:F2} MB ({memoryIncreasePercent:F1}%)");
            TestContext.WriteLine($"Memory growth trend: {memoryGrowthTrend / 1024:F2} KB per measurement");
        }

        [Test]
        [Category("LongRunning")]
        public async Task DiskSpaceStress_LoadTest()
        {
            // Arrange - Create large mods to stress disk I/O
            const int largeModCount = 20;
            const int filesPerMod = 100;
            var totalFilesCreated = 0;
            var totalBytesWritten = 0L;

            var stopwatch = Stopwatch.StartNew();

            // Act - Create and install large mods
            for (int i = 0; i < largeModCount; i++)
            {
                var archive = await CreateLargeTestArchiveAsync($"LargeMod{i:D2}", filesPerMod);
                var options = new InstallationOptions();
                var result = await _installationEngine!.InstallModAsync(archive, options, null, CancellationToken.None);

                totalFilesCreated += filesPerMod;
                totalBytesWritten += new FileInfo(archive).Length;

                // Rollback every few mods to manage disk space
                if (i % 5 == 4)
                {
                    await _installationEngine!.RollbackInstallationAsync(result.Id, CancellationToken.None);
                }
            }

            stopwatch.Stop();

            // Assert
            var throughput = (totalBytesWritten / 1024.0 / 1024.0) / (stopwatch.ElapsedMilliseconds / 1000.0);
            
            throughput.Should().BeGreaterThan(5); // At least 5 MB/s throughput
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(10)); // Should complete within 10 minutes
            
            TestContext.WriteLine($"Processed {totalFilesCreated} files ({totalBytesWritten / 1024 / 1024:F2} MB) in {stopwatch.ElapsedMilliseconds}ms");
            TestContext.WriteLine($"Disk I/O throughput: {throughput:F2} MB/s");
        }

        [Test]
        public async Task SearchPerformance_MassiveCollection()
        {
            // Arrange - Create a large collection with diverse mod names
            const int modCount = 1000;
            await CreateDiverseModCollectionAsync(modCount);
            
            // Load all mods into mod manager
            await _modManager!.LoadModsAsync();
            
            var searchTerms = new[] { "Weapon", "Armor", "Building", "Character", "Utility", "Enhancement", "Fix", "Overhaul" };
            var searchResults = new Dictionary<string, (int ResultCount, long ElapsedMs)>();

            // Act - Perform searches
            foreach (var term in searchTerms)
            {
                var stopwatch = Stopwatch.StartNew();
                var results = await _modManager.SearchModsAsync(term);
                stopwatch.Stop();
                
                searchResults[term] = (results.Count, stopwatch.ElapsedMilliseconds);
            }

            // Assert
            var averageSearchTime = searchResults.Values.Average(r => r.ElapsedMs);
            averageSearchTime.Should().BeLessThan(1000); // Should complete within 1 second on average
            
            foreach (var result in searchResults)
            {
                TestContext.WriteLine($"Search '{result.Key}': {result.Value.ResultCount} results in {result.Value.ElapsedMs}ms");
            }
            
            TestContext.WriteLine($"Average search time across {modCount} mods: {averageSearchTime:F2}ms");
        }

        private async Task CreateMassiveModCollectionAsync(int count)
        {
            var modsPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
            var tasks = new List<Task>();
            
            // Create mods in parallel for faster setup
            var semaphore = new SemaphoreSlim(Environment.ProcessorCount);
            
            for (int i = 0; i < count; i++)
            {
                var index = i;
                tasks.Add(Task.Run(async () =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        var modPath = Path.Combine(modsPath, $"MassiveMod{index:D4}");
                        Directory.CreateDirectory(modPath);
                        
                        await File.WriteAllTextAsync(Path.Combine(modPath, "enabled.txt"), "");
                        await File.WriteAllTextAsync(Path.Combine(modPath, "main.lua"), $"-- Massive test mod {index}");
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));
            }
            
            await Task.WhenAll(tasks);
        }

        private async Task CreateDiverseModCollectionAsync(int count)
        {
            var categories = new[] { "Weapon", "Armor", "Building", "Character", "Utility", "Enhancement", "Fix", "Overhaul" };
            var adjectives = new[] { "Enhanced", "Improved", "Advanced", "Ultimate", "Epic", "Legendary", "Custom", "Modded" };
            var random = new Random(42); // Fixed seed for reproducible tests
            
            var modsPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
            
            for (int i = 0; i < count; i++)
            {
                var category = categories[random.Next(categories.Length)];
                var adjective = adjectives[random.Next(adjectives.Length)];
                var modName = $"{adjective}{category}Mod{i:D4}";
                
                var modPath = Path.Combine(modsPath, modName);
                Directory.CreateDirectory(modPath);
                
                await File.WriteAllTextAsync(Path.Combine(modPath, "enabled.txt"), "");
                await File.WriteAllTextAsync(Path.Combine(modPath, "main.lua"), $"-- {modName}");
            }
        }

        private long CalculateMemoryGrowthTrend(List<long> memoryReadings)
        {
            if (memoryReadings.Count < 2) return 0;
            
            var differences = new List<long>();
            for (int i = 1; i < memoryReadings.Count; i++)
            {
                differences.Add(memoryReadings[i] - memoryReadings[i - 1]);
            }
            
            return (long)differences.Average();
        }

        private async Task<string> CreateLargeTestArchiveAsync(string modName, int fileCount)
        {
            var archivePath = Path.Combine(TestModsDirectory, $"{modName}.zip");
            var tempModDir = Path.Combine(TestModsDirectory, $"temp_{modName}");
            
            try
            {
                Directory.CreateDirectory(tempModDir);
                
                // Create multiple files to simulate a large mod
                for (int i = 0; i < fileCount; i++)
                {
                    var fileName = $"file_{i:D3}.txt";
                    var filePath = Path.Combine(tempModDir, fileName);
                    var content = $"Large mod file {i} content - " + new string('x', 2048); // 2KB per file
                    await File.WriteAllTextAsync(filePath, content);
                }
                
                // Create main mod files
                await File.WriteAllTextAsync(Path.Combine(tempModDir, "enabled.txt"), "");
                await File.WriteAllTextAsync(Path.Combine(tempModDir, "main.lua"), $"-- Large mod {modName}");
                
                // Create mock ZIP (in real implementation would use proper ZIP library)
                await File.WriteAllTextAsync(archivePath, $"Mock large ZIP archive for {modName}");
                
                return archivePath;
            }
            finally
            {
                if (Directory.Exists(tempModDir))
                {
                    Directory.Delete(tempModDir, true);
                }
            }
        }
    }
}
